<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
    />

    <title><PERSON><PERSON>j <PERSON> | Portfolio | Web Developer</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="app-container">
      <div class="layout-container">
        <header class="portfolio-header">
          <div class="header__brand">
            <!-- <div class="header__logo">
              <svg
                viewBox="0 0 48 48"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M42.4379 44C42.4379 44 36.0744 33.9038 41.1692 24C46.8624 12.9336 42.2078 4 42.2078 4L7.01134 4C7.01134 4 11.6577 12.932 5.96912 23.9969C0.876273 33.9029 7.27094 44 7.27094 44L42.4379 44Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div> -->
            <h2 class="header__title">Biraj Portfolio</h2>
          </div>
          <div class="header__nav-container">
            <nav class="portfolio-nav">
              <a class="portfolio-nav__link" href="#home">Home</a>
              <a class="portfolio-nav__link" href="#services">Services</a>
              <a class="portfolio-nav__link" href="#projects">Projects</a>
              <a class="portfolio-nav__link" href="#skills">Skills</a>
              <a class="portfolio-nav__link" href="#testimonials"
                >Testimonials</a
              >
              <a class="portfolio-nav__link" href="#about">About</a>
              <a class="portfolio-nav__link" href="#contact">Contact</a>
            </nav>

            <!-- Hamburger Menu Button -->
            <button class="hamburger" aria-label="Toggle menu">
              <span class="hamburger__line"></span>
              <span class="hamburger__line"></span>
              <span class="hamburger__line"></span>
            </button>
            <button class="btn btn--primary">
              <span>Let's Connect</span>
            </button>
          </div>
        </header>
        <div class="main-content">
          <div class="content-container">
            <div id="home" class="hero-section">
              <div class="hero">
                <div class="hero__image">
                  <div
                    class="hero__image-bg"
                    style="
                      background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuDdyvA7tyYTpB8SWAbRdkrQHENGqF0C9vFTTPQOBFE3zpPWVK4AzokMVp7_seroYIa6UY9YijknYuqaU-N-VhglxlRILQ15SaAI6wHqG3Z6tZlJ0l30iThKOQzSFP2bXjksk84lN-BGK2uj1g2HQLpA-MeMNX4RU8UgVv5AizlddoSzgYSgYlKZGRn5Nv01ZbepWP92atv822mBk9n-7OLTCo4GbDX9SikkJy5gjbSgqTrL44lkVQdTqvh4g4gjMTKtLH8eoQ2ewlY');
                    "
                  ></div>
                </div>
                <div class="hero__content">
                  <div class="hero__text animate-on-scroll">
                    <h1 class="hero__title animate-on-scroll">
                      Hey, I'm Biraj Desai — a Web Developer from Ahmedabad.
                    </h1>
                    <h2 class="hero__subtitle animate-on-scroll">
                      I don’t do templates. I build fast, scroll-stopping
                      websites powered by motion design and clean, modern code.
                      Using React, Tailwind, Framer Motion, and GSAP, I craft
                      custom web experiences that feel alive and are built to
                      convert.
                    </h2>
                  </div>
                  <div class="hero__buttons">
                    <a href="#projects" class="btn btn--primary" aria-label="View project showcase">
                      <span>View Projects</span>
                    </a>
                    <a href="assets/resume.pdf" download="Biraj_Desai_Resume.pdf" class="btn btn--secondary" aria-label="Download Biraj Desai's Resume PDF">
                      <span>Download Resume</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <h2 class="section-title animate-on-scroll">Connect With Me</h2>
            <p class="section-description animate-on-scroll">
              I’m Biraj Desai — a developer who loves turning ideas into fast,
              fluid websites. With a B.Tech in Computer Science from Indus
              University and certifications from platforms like Make, Google,
              and Postman, I bring both creative flair and technical discipline
              to every project.<br /><br />
              Whether I’m designing animated web experiences using React and
              Framer Motion or helping businesses go live with tailored,
              no-template sites, I’m all in. I’m currently growing my freelance
              brand alongside my full-time role, and I’m always excited to
              collaborate with brands that care about detail, speed, and
              uniqueness.
            </p>

            <!-- Skills -->
            <h2 id="skills" class="section-title animate-on-scroll">Skills</h2>
            <p class="section-description animate-on-scroll">
              My toolkit blends design thinking with front-end engineering.
              Whether it's building custom UI animations with GSAP or
              translating mockups into high-performance React components, I
              combine clean code and visual polish to deliver interactive,
              modern web experiences.
            </p>

            <div class="skills__grid">
              <!-- Languages Category -->
              <div class="skills__category animate-on-scroll">
                <h3 class="skills__category-title">Languages</h3>
                <div class="skills__category-grid">
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">JavaScript (ES6+)</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">TypeScript</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">HTML5</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">CSS3</h4>
                  </div>
                </div>
              </div>

              <!-- Frameworks & Libraries Category -->
              <div class="skills__category animate-on-scroll">
                <h3 class="skills__category-title">Frameworks & Libraries</h3>
                <div class="skills__category-grid">
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M223.68,66.15,135.68,18a15.94,15.94,0,0,0-15.36,0l-88,48.18a16,16,0,0,0-8.32,14v95.64a16,16,0,0,0,8.32,14l88,48.17a15.88,15.88,0,0,0,15.36,0l88-48.17a16,16,0,0,0,8.32-14V80.18A16,16,0,0,0,223.68,66.15ZM168,152v50.09l-32,17.52V132.74l80-43.8v32l-43.84,24A8,8,0,0,0,168,152Zm-84.16-7L40,121v-32l80,43.8v86.87L88,202.09V152A8,8,0,0,0,83.84,145Zm-.7-88.41,41,22.45a8,8,0,0,0,7.68,0l41-22.45,34.48,18.87L128,118.88,48.66,75.44ZM128,32h0l28.2,15.44L128,62.89,99.8,47.45ZM40,139.22l32,17.52v36.59L40,175.82Zm144,54.11V156.74l32-17.52v36.6Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">React</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M223.68,66.15,135.68,18a15.94,15.94,0,0,0-15.36,0l-88,48.18a16,16,0,0,0-8.32,14v95.64a16,16,0,0,0,8.32,14l88,48.17a15.88,15.88,0,0,0,15.36,0l88-48.17a16,16,0,0,0,8.32-14V80.18A16,16,0,0,0,223.68,66.15ZM168,152v50.09l-32,17.52V132.74l80-43.8v32l-43.84,24A8,8,0,0,0,168,152Zm-84.16-7L40,121v-32l80,43.8v86.87L88,202.09V152A8,8,0,0,0,83.84,145Zm-.7-88.41,41,22.45a8,8,0,0,0,7.68,0l41-22.45,34.48,18.87L128,118.88,48.66,75.44ZM128,32h0l28.2,15.44L128,62.89,99.8,47.45ZM40,139.22l32,17.52v36.59L40,175.82Zm144,54.11V156.74l32-17.52v36.6Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Next.js</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M227.08,64.62l-96-40a7.93,7.93,0,0,0-6.16,0l-96,40a8,8,0,0,0-4.85,8.44l16,120a8,8,0,0,0,4.35,6.1l80,40a8,8,0,0,0,7.16,0l80-40a8,8,0,0,0,4.35-6.1l16-120A8,8,0,0,0,227.08,64.62ZM200.63,186.74,128,223.06,55.37,186.74,40.74,77,128,40.67,215.26,77ZM121,84.12l-40,72a8,8,0,1,0,14,7.76L106,144H150l11,19.88a8,8,0,1,0,14-7.76l-40-72a8,8,0,0,0-14,0ZM141.07,128H114.93L128,104.47Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Node.js</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M227.08,64.62l-96-40a7.93,7.93,0,0,0-6.16,0l-96,40a8,8,0,0,0-4.85,8.44l16,120a8,8,0,0,0,4.35,6.1l80,40a8,8,0,0,0,7.16,0l80-40a8,8,0,0,0,4.35-6.1l16-120A8,8,0,0,0,227.08,64.62ZM200.63,186.74,128,223.06,55.37,186.74,40.74,77,128,40.67,215.26,77ZM121,84.12l-40,72a8,8,0,1,0,14,7.76L106,144H150l11,19.88a8,8,0,1,0,14-7.76l-40-72a8,8,0,0,0-14,0ZM141.07,128H114.93L128,104.47Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Express.js</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M176,16H80A24,24,0,0,0,56,40V216a24,24,0,0,0,24,24h96a24,24,0,0,0,24-24V40A24,24,0,0,0,176,16ZM72,64H184V192H72Zm8-32h96a8,8,0,0,1,8,8v8H72V40A8,8,0,0,1,80,32Zm96,192H80a8,8,0,0,1-8-8v-8H184v8A8,8,0,0,1,176,224Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">GSAP</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M176,16H80A24,24,0,0,0,56,40V216a24,24,0,0,0,24,24h96a24,24,0,0,0,24-24V40A24,24,0,0,0,176,16ZM72,64H184V192H72Zm8-32h96a8,8,0,0,1,8,8v8H72V40A8,8,0,0,1,80,32Zm96,192H80a8,8,0,0,1-8-8v-8H184v8A8,8,0,0,1,176,224Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Framer Motion</h4>
                  </div>
                </div>
              </div>

              <!-- Design Category -->
              <div class="skills__category animate-on-scroll">
                <h3 class="skills__category-title">Design</h3>
                <div class="skills__category-grid">
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M184,96a40,40,0,0,0-24-72H88A40,40,0,0,0,64,96a40,40,0,0,0,1.37,65A44,44,0,1,0,136,196V160a40,40,0,1,0,48-64Zm0-32a24,24,0,0,1-24,24H136V40h24A24,24,0,0,1,184,64ZM64,64A24,24,0,0,1,88,40h32V88H88A24,24,0,0,1,64,64Zm24,88a24,24,0,0,1,0-48h32v48H88Zm32,44a28,28,0,1,1-28-28h28Zm40-44a24,24,0,1,1,24-24A24,24,0,0,1,160,152Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Figma</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M227.31,73.37,182.63,28.68a16,16,0,0,0-22.63,0L36.69,152A15.86,15.86,0,0,0,32,163.31V208a16,16,0,0,0,16,16H92.69A15.86,15.86,0,0,0,104,219.31L227.31,96a16,16,0,0,0,0-22.63ZM92.69,208H48V163.31l88-88L180.69,120ZM192,108.68,147.31,64l24-24L216,84.68Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">UI/UX Design</h4>
                  </div>
                </div>
              </div>

              <!-- Tools Category -->
              <div class="skills__category animate-on-scroll">
                <h3 class="skills__category-title">Tools</h3>
                <div class="skills__category-grid">
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Git & GitHub</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M224,48H32A16,16,0,0,0,16,64V192a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V64A16,16,0,0,0,224,48ZM32,64H224V88H32ZM32,192V104H224v88Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Vercel & Netlify</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M176,16H80A24,24,0,0,0,56,40V216a24,24,0,0,0,24,24h96a24,24,0,0,0,24-24V40A24,24,0,0,0,176,16ZM72,64H184V192H72Zm8-32h96a8,8,0,0,1,8,8v8H72V40A8,8,0,0,1,80,32Zm96,192H80a8,8,0,0,1-8-8v-8H184v8A8,8,0,0,1,176,224Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Postman</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM40,56H216V88H40ZM40,200V104H216v96Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Contentful</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM40,56H216V88H40ZM40,200V104H216v96Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Strapi</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">SCSS</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">Jest</h4>
                  </div>
                  <div class="skills__card">
                    <div class="skills__icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24px"
                        height="24px"
                        fill="currentColor"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                        />
                      </svg>
                    </div>
                    <h4 class="skills__title">React Testing Library</h4>
                  </div>
                </div>
              </div>
            </div>

            <!-- Projects  -->
            <h2 class="section-title animate-on-scroll">Project Showcase</h2>
            <h3 class="section-subtitle animate-on-scroll">Web Designs</h3>

            <div class="projects__container">
              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('./assets/Biraj-Desai-Web-Designer-Full-Stack-Developer-07-04-2025_08_39_AM.jpg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Web Dev Portfolio</h4>
                  <p class="projects__description">
                    A modern, responsive portfolio website showcasing web
                    development skills with clean design, smooth animations, and
                    interactive elements. Built with HTML5, CSS3, and JavaScript
                    featuring a professional layout optimized for both desktop
                    and mobile devices.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://biraj2692.github.io/Web-Designer-and-Developer-Portfolio/"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/biraj2692/Web-Designer-and-Developer-Portfolio/"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>

              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('./assets/Fashionwerk-Portfolio-Branding-Agency-Berlin-07-04-2025_08_39_AM.jpg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Branding Agency Portfolio</h4>
                  <p class="projects__description">
                    Fashionwerk - A sophisticated branding agency portfolio from
                    Berlin featuring elegant typography, premium visual design,
                    and strategic brand positioning. Showcases creative services
                    with a focus on fashion and luxury brand development.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://biraj2692.github.io/Fashionwerk-Portfolio---Branding-Agency-Berlin/"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/biraj2692/Fashionwerk-Portfolio---Branding-Agency-Berlin/"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>

              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('./assets/Digital-Designer-07-04-2025_08_39_AM.jpg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Digital Designer Landing Page</h4>
                  <p class="projects__description">
                    A creative and visually striking landing page designed for
                    digital designers and creative professionals. Features bold
                    typography, engaging visual elements, and a modern layout
                    that effectively showcases creative work and services.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://biraj2692.github.io/08.-Digital-Designer-Landing-Page/"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/biraj2692/08.-Digital-Designer-Landing-Page/"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>

              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('./assets/LUXE-ATELIER-Premium-Fashion-07-04-2025_08_39_AM.jpg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Fashion Brand Web Design</h4>
                  <p class="projects__description">
                    Premium fashion web design featuring luxurious aesthetics,
                    high-end product showcases, and sophisticated user
                    experience. Designed to elevate fashion brands with elegant
                    layouts, stunning visuals, and seamless e-commerce
                    integration.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://biraj2692.github.io/03.-Premium-Fashion-Web-Design/"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/biraj2692/03.-Premium-Fashion-Web-Design/"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>

              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('./assets/Wanderlust-Travel-Agency-Your-Gateway-to-Adventure-07-04-2025_08_39_AM.jpg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Travel Agency Landing Page</h4>
                  <p class="projects__description">
                    An inspiring travel agency website designed to capture
                    wanderlust and drive bookings. Features stunning destination
                    imagery, intuitive navigation, and compelling
                    calls-to-action that convert visitors into travelers.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://biraj2692.github.io/travel-agency-web-design/"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/biraj2692/travel-agency-web-design/"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>

              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('./assets/Dental-Care-Landing-Page-07-04-2025_08_39_AM.jpg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Dental Care Landing Page</h4>
                  <p class="projects__description">
                    A professional dental care landing page designed to build
                    trust and attract patients. Features clean medical
                    aesthetics, service highlights, appointment booking
                    functionality, and patient testimonials for credibility.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://biraj2692.github.io/07.-Dental-Care-Landing-Page/"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/biraj2692/07.-Dental-Care-Landing-Page/"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>

              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('./assets/Biraj-Desai-Product-Designer-07-04-2025_08_38_AM.jpg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">
                    Visual and Product Designer Web Design
                  </h4>
                  <p class="projects__description">
                    A creative portfolio website for visual and product
                    designers showcasing innovative design thinking,
                    user-centered approach, and aesthetic excellence. Features
                    interactive elements and compelling case studies.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://biraj2692.github.io/09.-Visual-Designer-Landing-Page/"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/biraj2692/09.-Visual-Designer-Landing-Page"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <p class="projects__scroll-hint">
              ← Scroll horizontally to view more projects →
            </p>

            <h3 id="projects" class="section-subtitle animate-on-scroll">
              Minor Projects
            </h3>
            <div class="projects__container">
              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBm4oth8FoaogbvWY-BWfnnfa0r7pG6Za4lCYX8GIMfdKpM13tj4CevAYDVklPuAhBAh_Ii5vhjtBv_lXtv5CVdf_ojMFF8qbLeCXyKIbJy3S6b3sKGr2AlfMhmI8_kzj-58I06yzeztIxbk_G2M39YvfcLN9DW97rqYPvmfnCsn2VJcNn0lhGs_5A4S40fPYc9RRrExzR2G66hP53gcgrWLBdcrYlOokoIcpmJuswG3-bCcMtnpRQZBG3ocw5mFUheOBItLpqf8xc');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Project Beta</h4>
                  <p class="projects__description">
                    A small web application for a local business.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://beta.com"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                      aria-label="View Project Beta live demo"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/beta"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                      aria-label="View Project Beta source code on GitHub"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>
              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBzmolpsvRd772E6nUCsPubF2Fi1mV8EVPHiqAL2ynajSSKbmDdSXO5uY-K9mSGd3dFEHhKfVn32QKnz50L18iXlxBN8JbB3IlWAqyTPYXt9EvbU3lpL5fDT3JDniU0IeV1NEqG7Fk1EmAkD5_9siWvzSlUgWZk7nJNKxJb8VTk85biLphiNpAYMCj4yGd3AFRQ3Ou6TS4QxuIWKXnTLWUDF0DTRid-WhXWhEmPlp9A4O0dbUBw6NPnOQ6Tvh7pM0Ln5f7AufGUOzk');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Project Gamma</h4>
                  <p class="projects__description">
                    A landing page for a marketing campaign.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://gamma.com"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                      aria-label="View Project Gamma live demo"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/gamma"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                      aria-label="View Project Gamma source code on GitHub"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>
              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuCai4rc-GiPW7yO3hJETGHxY88EC8_qzWEJfCyODL8-sitfdxAX-BsoDQtYCBgXPS8680M4i-LmqZRQax7GABIMwkYovCsABENdDUB4RMxzZbCAfaZoh8tM-nkkDaFOlUcpC-3Y-Ssub1XfU4EcVckD2Szf-JoV3tDd_zDOl5nPf1_eSUy4LamFB8DGt6rCgEkOBYLKO9g4cqTr8dj78OJQX9AUeul5IJkOmmFGu7EGVJN31Zjo3EWC-MFFUlrd2q5JHTzL6KxgXwk');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Project Delta</h4>
                  <p class="projects__description">
                    A simple mobile app prototype.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://delta.com"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                      aria-label="View Project Delta live demo"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/delta"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                      aria-label="View Project Delta source code on GitHub"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <p class="projects__scroll-hint">
              ← Scroll horizontally to view more projects →
            </p>

            <h3 class="section-subtitle animate-on-scroll">Major Projects</h3>
            <div class="projects__container">
              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuDuxKfN7o6qHxWiB22sP-7XPsGiULdWEBSAO3yBxr_sy8w1pGI0gn4WMmUcs6xBwu7DMo5P6lmsCaIJPVu4a7gupBFS8Yh8CYulddyTtYzEz9BqzbGZ4QpWE8-IyiacpG4gO_S0-5zMM1PlxeSNQ8rOGZaOl-rmMijW4bxNlPW27uGwRHHqSIqABg3Z_aFu6ML1GU6-GBV9XdjKEUfgS5k1CxC8vfDTFB8EQ-KRrF70UTQzshQ4to2dattvrKcJ-9_qmvzPf_5b3Zg');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Project Omega</h4>
                  <p class="projects__description">
                    A complex web platform for a large corporation.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://omega.com"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                      aria-label="View Project Omega live demo"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/omega"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                      aria-label="View Project Omega source code on GitHub"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>
              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuCLtSyWCP0H5vDCzJok4USahzBbI9E0Zcx70GnIH2XYo4G6sqMREKR8w4dw6GKzuqOpWlf8cteYSiY3lYYO7-v60AQ292_4V_GPAa2WGhuE5sEmcT7pktyPkYBVD3QyxcI0CrqoP9ELRdkbROmtpFMVR5_hNQV_xRTBRwlLBIDqOdh7E0oZ7ZbDfAyiJKsEKmMpGyxxhT4bXX5Ul0DYR1GxAAOFj6gIuS4Jbm8_pHtgni564P2oHqNSaWrQUjPFRsEi1_WG9hTipC4');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Project Sigma</h4>
                  <p class="projects__description">
                    A comprehensive e-commerce website.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://sigma.com"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                      aria-label="View Project Sigma live demo"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/sigma"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                      aria-label="View Project Sigma source code on GitHub"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>
              <div class="projects__card animate-on-scroll">
                <div
                  class="projects__image"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuAr_kr5_gMUGINSHRB1I0K6IVN0vXjSlvKwim5CTsjFtb4tJQRdm3wkr1jsuF85-A1QBLS-GiTGmem644WQdxFVZyAPvxRj19HpUp5uWlQZxZaY-3Wx6HsZOXrW2_VJQBW8CgiZBY2OEMOI475byq8RsoRF8ijTDMZKNX3vvBG_F-zSxgVu7hXnNVmR6kx5-An7zY-95jcbuGSZqoEldi1we2tflZxkMcDJ-7hG9_BkutI-u43Hl6YzEnind-qgrJJSKkSiCrdKSsc');
                  "
                >
                  <div class="projects__overlay">
                    <span>View Project</span>
                  </div>
                </div>
                <div class="projects__content">
                  <h4 class="projects__title">Project Alpha</h4>
                  <p class="projects__description">
                    A full-featured mobile application for a social network.
                  </p>
                  <div class="projects__buttons">
                    <a
                      href="https://alpha.com"
                      class="projects__btn projects__btn--primary"
                      target="_blank"
                      aria-label="View Project Alpha live demo"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M200,64V168a8,8,0,0,1-16,0V83.31L69.66,197.66a8,8,0,0,1-11.32-11.32L172.69,72H88a8,8,0,0,1,0-16H192A8,8,0,0,1,200,64Z"
                        />
                      </svg>
                      Live Demo
                    </a>
                    <a
                      href="https://github.com/alpha"
                      class="projects__btn projects__btn--secondary"
                      target="_blank"
                      aria-label="View Project Alpha source code on GitHub"
                    >
                      <svg
                        class="projects__btn-icon"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 256 256"
                      >
                        <path
                          d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68Z"
                        />
                      </svg>
                      GitHub
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <p class="projects__scroll-hint">
              ← Scroll horizontally to view more projects →
            </p>

            <!-- Education -->

            <h2 class="section-title animate-on-scroll">Education</h2>
            <div class="education-section animate-on-scroll">
              <div class="education__card">
                <div class="education__image">
                  <div class="education__image-placeholder"></div>
                </div>
                <div class="education__content">
                  <h3 class="education__title">Indus University, Ahmedabad</h3>
                  <div class="education__details">
                    <p class="education__description">
                      Graduated with Honors in B.Tech Computer Science &
                      Technology. Key coursework: Advanced Algorithms, Data
                      Structures, Software Engineering, Web Technologies.
                    </p>
                    <p class="education__degree">Dec 2020 – Dec 2024</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Experience -->
            <h2 class="section-title animate-on-scroll">Experience</h2>
            <div class="experience-section animate-on-scroll">
              <!-- GDG Cloud Gandhinagar Volunteer -->
              <div class="experience__card">
                <div class="experience__image">
                  <div class="experience__image-placeholder"></div>
                </div>
                <div class="experience__content">
                  <h3 class="experience__title">Community Volunteer</h3>
                  <div class="experience__details">
                    <p class="experience__description">
                      • Organized and spoke at GDG Cloud Gandhinagar 25 by
                      Google, driving engagement for 800+ attendees.<br />
                      • Coordinated workshops on Cloud and AI development best
                      practices and modern tooling.<br />
                    </p>
                    <p class="experience__position">
                      GDG Cloud Gandhinagar – Apr 2025 to Present
                    </p>
                  </div>
                </div>
              </div>
              <!-- Support Associate -->
              <div class="experience__card">
                <div class="experience__image">
                  <div class="experience__image-placeholder"></div>
                </div>
                <div class="experience__content">
                  <h3 class="experience__title">Support Associate</h3>
                  <div class="experience__details">
                    <p class="experience__description">
                      • Provided user support for Salesmate CRM reducing
                      resolution time by 20%.<br />
                      • Collaborated with the dev team to test and refine UI/UX
                      updates.<br />
                      • Created 10+ knowledge‑base articles and troubleshooting
                      guides.
                    </p>
                    <p class="experience__position">
                      Rapidops – Dec 2023 - Present
                    </p>
                  </div>
                </div>
              </div>

              <!-- Freelance Web Developer -->
              <div class="experience__card">
                <div class="experience__image">
                  <div class="experience__image-placeholder"></div>
                </div>
                <div class="experience__content">
                  <h3 class="experience__title">Freelance Web Developer</h3>
                  <div class="experience__details">
                    <p class="experience__description">
                      • Built 5+ custom websites (React, Tailwind, GSAP) for
                      startups & small businesses.<br />
                      • Achieved 100% client satisfaction by delivering
                      on‑brand, performant sites within deadlines.<br />
                      • Managed end‑to‑end workflow: design mockups,
                      development, deployment, and ongoing maintenance.
                    </p>
                    <p class="experience__position">
                      Self‑Employed – March 2025 to Present
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- About Me -->
            <h2 id="about" class="section-title animate-on-scroll">About Me</h2>
            <div class="about-section animate-on-scroll">
              <div class="about__content">
                <p class="section-description">
                  I'm a passionate creative professional with a deep love for
                  crafting digital experiences that seamlessly blend
                  functionality with aesthetic appeal. With over 5 years in the
                  industry, I've had the privilege of working with diverse
                  clients, from innovative startups to established corporations.
                </p>
                <p class="section-description">
                  My journey began with a fascination for how design and
                  technology can solve real-world problems. I believe that great
                  design is not just about how something looks, but how it works
                  and how it makes people feel. Every project I undertake is an
                  opportunity to create something meaningful and impactful.
                </p>
                <div class="about__stats">
                  <div class="stat">
                    <h3 class="stat__number">50+</h3>
                    <p class="stat__label">Projects Completed</p>
                  </div>
                  <div class="stat">
                    <h3 class="stat__number">5+</h3>
                    <p class="stat__label">Years Experience</p>
                  </div>
                  <div class="stat">
                    <h3 class="stat__number">100%</h3>
                    <p class="stat__label">Client Satisfaction</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Services Section -->
            <h2 id="services" class="section-title animate-on-scroll">
              Services
            </h2>
            <p class="section-description animate-on-scroll">
              From a quick audit to a full-blown launch, I offer design and
              development services tailored for startups, creators, and
              businesses that value originality. Backed by real projects,
              certifications, and years of hands-on experience, my goal is to
              build things that perform, not just look good.
            </p>
            <div class="services__container">
              <div class="services__card animate-on-scroll">
                <div class="services__icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32px"
                    height="32px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-57.67-11.19-79.93-33.25A133.47,133.47,0,0,1,25,128,133.33,133.33,0,0,1,48.07,97.25C70.33,75.19,97.22,64,128,64s57.67,11.19,79.93,33.25A133.46,133.46,0,0,1,231.05,128C223.84,141.46,192.43,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Z"
                    />
                  </svg>
                </div>
                <h3 class="services__title">Free Website Audit</h3>
                <div class="services__price">Free</div>
                <p class="services__description">
                  Get an in-depth audit of your current website’s design,
                  performance, and user experience. I’ll send you a short,
                  actionable report with insights to improve speed, UX, and
                  overall quality.
                </p>
                <ul class="services__features">
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    Performance & loading speed review
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    UX & UI evaluation
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    Detailed improvement report
                  </li>
                </ul>
                <button class="btn btn--primary services__cta">
                  <span>Get Free Audit</span>
                </button>
              </div>

              <div
                class="services__card services__card--featured animate-on-scroll"
              >
                <div class="services__badge">Most Popular</div>
                <div class="services__icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32px"
                    height="32px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM40,56H216V88H40ZM40,200V104H216v96Z"
                    />
                  </svg>
                </div>
                <h3 class="services__title">₹999 Concept Pack</h3>
                <div class="services__price">₹999</div>
                <p class="services__description">
                  A low-risk way to get started. I’ll deliver design-ready
                  wireframes, mockups, and a style guide that gives your project
                  a clear creative direction—great for startups, solopreneurs,
                  and side projects.
                </p>
                <ul class="services__features">
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    3 page wireframes
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    High-fidelity mockups
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    Brand style guide
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    2 revision rounds
                  </li>
                </ul>
                <button class="btn btn--primary services__cta">
                  <span>Get Concept Pack</span>
                </button>
              </div>

              <div class="services__card animate-on-scroll">
                <div class="services__icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32px"
                    height="32px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M224,48H32A16,16,0,0,0,16,64V192a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V64A16,16,0,0,0,224,48ZM32,64H224V88H32ZM32,192V104H224v88Z"
                    />
                  </svg>
                </div>
                <h3 class="services__title">Full Build</h3>
                <div class="services__price">Custom Quote</div>
                <p class="services__description">
                  End-to-end development for your brand or business. From
                  strategy and design to development and launch — you’ll get a
                  fast, responsive, SEO-ready website that’s built to scale and
                  stand out.
                </p>
                <ul class="services__features">
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    Complete design & development
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    Responsive & mobile-optimized
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    Basic SEO & performance optimization
                  </li>
                  <li class="services__feature">
                    <svg
                      class="services__check"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="m229.66,77.66-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"
                      />
                    </svg>
                    Support & maintenance add-ons available
                  </li>
                </ul>
                <button class="btn btn--secondary services__cta">
                  <span>Get Custom Quote</span>
                </button>
              </div>
            </div>

            <!-- Testimonials Section
            <h2 id="testimonials" class="section-title animate-on-scroll">
              What Clients Say
            </h2>
            <p class="section-description animate-on-scroll">
              Don't just take my word for it. Here's what clients and
              collaborators have to say about working with me.
            </p>
            <div class="testimonials__container">
              <div class="testimonials__card animate-on-scroll">
                <div class="testimonials__quote">
                  <svg
                    class="testimonials__quote-icon"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M100,56H40A16,16,0,0,0,24,72v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,100,56ZM40,136V72H100v64Zm176-80H156a16,16,0,0,0-16,16v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,216,56ZM156,136V72h60v64Z"
                    />
                  </svg>
                  <p class="testimonials__text">
                    "Alex delivered an exceptional website that perfectly
                    captured our brand vision. The attention to detail and user
                    experience was outstanding. Our conversion rate increased by
                    40% within the first month of launch."
                  </p>
                </div>
                <div class="testimonials__author">
                  <div class="testimonials__avatar">
                    <img
                      src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face"
                      alt="Sarah Johnson"
                    />
                  </div>
                  <div class="testimonials__info">
                    <h4 class="testimonials__name">Sarah Johnson</h4>
                    <p class="testimonials__role">CEO, TechStart Solutions</p>
                  </div>
                </div>
              </div>

              <div class="testimonials__card animate-on-scroll">
                <div class="testimonials__quote">
                  <svg
                    class="testimonials__quote-icon"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M100,56H40A16,16,0,0,0,24,72v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,100,56ZM40,136V72H100v64Zm176-80H156a16,16,0,0,0-16,16v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,216,56ZM156,136V72h60v64Z"
                    />
                  </svg>
                  <p class="testimonials__text">
                    "Working with Alex was a game-changer for our e-commerce
                    platform. The mobile-first approach and seamless user
                    interface resulted in a 60% increase in mobile sales. Highly
                    recommended!"
                  </p>
                </div>
                <div class="testimonials__author">
                  <div class="testimonials__avatar">
                    <img
                      src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face"
                      alt="Michael Chen"
                    />
                  </div>
                  <div class="testimonials__info">
                    <h4 class="testimonials__name">Michael Chen</h4>
                    <p class="testimonials__role">Founder, EcoMarket</p>
                  </div>
                </div>
              </div>

              <div class="testimonials__card animate-on-scroll">
                <div class="testimonials__quote">
                  <svg
                    class="testimonials__quote-icon"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M100,56H40A16,16,0,0,0,24,72v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,100,56ZM40,136V72H100v64Zm176-80H156a16,16,0,0,0-16,16v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,216,56ZM156,136V72h60v64Z"
                    />
                  </svg>
                  <p class="testimonials__text">
                    "The redesign Alex created for our nonprofit completely
                    transformed our online presence. Donations increased by 85%
                    and volunteer sign-ups doubled. The accessibility features
                    were particularly impressive."
                  </p>
                </div>
                <div class="testimonials__author">
                  <div class="testimonials__avatar">
                    <img
                      src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=64&h=64&fit=crop&crop=face"
                      alt="Emily Rodriguez"
                    />
                  </div>
                  <div class="testimonials__info">
                    <h4 class="testimonials__name">Emily Rodriguez</h4>
                    <p class="testimonials__role">Director, Hope Foundation</p>
                  </div>
                </div>
              </div>
            </div>

            Community Involvement
            <div class="community-section animate-on-scroll">
              <h3 class="section-subtitle">Community Involvement</h3>
              <div class="community__card">
                <div class="community__icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="48px"
                    height="48px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216ZM173.66,90.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,140.69l50.34-50.35A8,8,0,0,1,173.66,90.34Z"
                    />
                  </svg>
                </div>
                <div class="community__content">
                  <h4 class="community__title">
                    Google Developer Groups Gandhinagar (GDG CCD)
                  </h4>
                  <p class="community__description">
                    Active volunteer and organizer with GDG Chandigarh,
                    contributing to the local tech community through workshops,
                    mentoring sessions, and event organization. Helped organize
                    5+ major tech events reaching over 500+ developers and
                    students.
                  </p>
                  <div class="community__achievements">
                    <div class="achievement">
                      <span class="achievement__number">5+</span>
                      <span class="achievement__label">Events Organized</span>
                    </div>
                    <div class="achievement">
                      <span class="achievement__number">500+</span>
                      <span class="achievement__label">Developers Reached</span>
                    </div>
                    <div class="achievement">
                      <span class="achievement__number">50+</span>
                      <span class="achievement__label">Students Mentored</span>
                    </div>
                  </div>
                </div>
              </div>
            </div> -->

            <!-- Contact Me -->
            <h2 id="contact" class="section-title animate-on-scroll">
              Contact Us
            </h2>
            <form class="contact-form animate-on-scroll">
              <div class="form__group">
                <label class="form__label" for="name">Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  placeholder="Your Name"
                  class="form__input"
                  required
                />
              </div>
              <div class="form__group">
                <label class="form__label" for="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Your Email"
                  class="form__input"
                  required
                />
              </div>
              <div class="form__group">
                <label class="form__label" for="phone">Phone</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  placeholder="Your Phone Number"
                  class="form__input"
                />
              </div>
              <div class="form__group">
                <label class="form__label" for="subject">Subject</label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  placeholder="Subject"
                  class="form__input"
                />
              </div>
              <div class="form__group">
                <label class="form__label" for="message">Message</label>
                <textarea
                  id="message"
                  name="message"
                  placeholder="Your Message"
                  class="form__textarea"
                  required
                ></textarea>
              </div>
              <div class="form__group">
                <button type="submit" class="btn btn--primary">
                  <span>Send Message</span>
                </button>
              </div>
            </form>
            <p class="contact-info animate-on-scroll">
              Email: <a href="mailto:<EMAIL>" class="contact-link" aria-label="Send email to Biraj Desai"><EMAIL></a> |
              Phone: <a href="tel:+916354556465" class="contact-link" aria-label="Call Biraj Desai">+91 63545 56465</a>
            </p>
            <!-- Social Media -->
            <div class="social-media-section animate-on-scroll">
              <div class="social-media__container">
                <a href="https://www.linkedin.com/in/biraj-desai/" target="_blank" rel="noopener noreferrer" class="social-media__card" aria-label="Visit Biraj Desai's LinkedIn profile">
                  <div class="social-media__icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20px"
                      height="20px"
                      fill="currentColor"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"
                      ></path>
                    </svg>
                  </div>
                  <p class="social-media__label">LinkedIn</p>
                </a>
                <a href="https://github.com/biraj2692" target="_blank" rel="noopener noreferrer" class="social-media__card" aria-label="Visit Biraj Desai's GitHub profile">
                  <div class="social-media__icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20px"
                      height="20px"
                      fill="currentColor"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"
                      ></path>
                    </svg>
                  </div>
                  <p class="social-media__label">GitHub</p>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <footer class="portfolio-footer">
          <div class="footer__container">
            <div class="footer__content">
              <div class="footer__nav">
                <a class="footer__link" href="#">Back to top</a>
              </div>
              <div class="footer__social">
                <a href="https://twitter.com/biraj_desai" target="_blank" rel="noopener noreferrer" class="footer__social-link" aria-label="Follow Biraj Desai on Twitter">
                  <div class="footer__social-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24px"
                      height="24px"
                      fill="currentColor"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"
                      ></path>
                    </svg>
                  </div>
                </a>
                <a href="https://www.instagram.com/biraj_desai/" target="_blank" rel="noopener noreferrer" class="footer__social-link" aria-label="Follow Biraj Desai on Instagram">
                  <div class="footer__social-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24px"
                      height="24px"
                      fill="currentColor"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160ZM176,24H80A56.06,56.06,0,0,0,24,80v96a56.06,56.06,0,0,0,56,56h96a56.06,56.06,0,0,0,56-56V80A56.06,56.06,0,0,0,176,24Zm40,152a40,40,0,0,1-40,40H80a40,40,0,0,1-40-40V80A40,40,0,0,1,80,40h96a40,40,0,0,1,40,40ZM192,76a12,12,0,1,1-12-12A12,12,0,0,1,192,76Z"
                      ></path>
                    </svg>
                  </div>
                </a>
                <a href="https://dribbble.com/biraj_desai" target="_blank" rel="noopener noreferrer" class="footer__social-link" aria-label="View Biraj Desai's work on Dribbble">
                  <div class="footer__social-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24px"
                      height="24px"
                      fill="currentColor"
                      viewBox="0 0 256 256"
                    >
                      <path
                        d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm87.65,96.18Q211.83,120,208,120a168.58,168.58,0,0,0-43.94,5.84A166.52,166.52,0,0,0,150.61,96a168.32,168.32,0,0,0,38.2-31.55A87.78,87.78,0,0,1,215.65,120.18ZM176.28,54.46A151.75,151.75,0,0,1,142,82.52a169.22,169.22,0,0,0-38.63-39,88,88,0,0,1,73,10.94ZM85.65,50.88a153.13,153.13,0,0,1,42,39.18A151.82,151.82,0,0,1,64,104a154.19,154.19,0,0,1-20.28-1.35A88.39,88.39,0,0,1,85.65,50.88ZM40,128a87.73,87.73,0,0,1,.53-9.64A168.85,168.85,0,0,0,64,120a167.84,167.84,0,0,0,72.52-16.4,150.82,150.82,0,0,1,12.31,27.13,167.11,167.11,0,0,0-24.59,11.6,169.22,169.22,0,0,0-55.07,51.06A87.8,87.8,0,0,1,40,128Zm42,75a152.91,152.91,0,0,1,50.24-46.79a148.81,148.81,0,0,1,20.95-10,152.48,152.48,0,0,1,3.73,33.47,152.93,152.93,0,0,1-3.49,32.56A87.92,87.92,0,0,1,82,203Zm89.06,1.73a170,170,0,0,0,1.86-25,168.69,168.69,0,0,0-4.45-38.47A152.31,152.31,0,0,1,208,136q3.8,0,7.61.19A88.13,88.13,0,0,1,171.06,204.72Z"
                      ></path>
                    </svg>
                  </div>
                </a>
              </div>
              <p class="footer__copyright">
                Made with 💓 and 🧑‍💻 by Biraj &copy; 2025
              </p>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <script src="script.js"></script>
  </body>
</html>
